<?php
// تحديد المسار الجذر
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

// تحميل ملفات التكوين أولاً
require_once 'config/config.php';
require_once 'includes/functions.php';

// بدء الجلسة بعد تحميل التكوين
session_start();

// معالج AJAX لتحميل المزيد من المقالات
if (isset($_GET['ajax']) && $_GET['ajax'] == '1') {
    header('Content-Type: application/json');

    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $per_page = (int)getSetting('articles_per_page', 12);

    try {
        $articles = getArticles($page, $per_page);

        if (!empty($articles)) {
            echo json_encode([
                'success' => true,
                'articles' => $articles,
                'page' => $page,
                'per_page' => $per_page,
                'has_more' => count($articles) == $per_page
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'لا توجد مقالات أخرى',
                'articles' => []
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء تحميل المقالات',
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

// إعداد الصفحة
$page_title = 'الرئيسية';
$page_description = getSetting('site_description', 'موقع إخباري شامل');

// الحصول على رقم الصفحة
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = (int)getSetting('articles_per_page', 12);

// الحصول على المقالات
$articles = getArticles($page, $per_page);
$total_articles = getTotalArticles();
$total_pages = ceil($total_articles / $per_page);

// الحصول على المقالات المميزة
$featured_articles = getFeaturedArticles(4);

// الحصول على أحدث المقالات للشريط الجانبي
$latest_articles = getLatestArticles(8);

// الحصول على المباريات القادمة (إذا كان نظام المباريات مفعل)
$upcoming_matches = [];
if (file_exists('includes/matches_functions.php')) {
    require_once 'includes/matches_functions.php';
    try {
        $upcoming_matches = getUpcomingMatches(5);
    } catch (Exception $e) {
        // نظام المباريات غير مفعل أو غير مثبت
        $upcoming_matches = [];
    }
}

include 'includes/header.php';
?>



<!-- Football Matches Widget -->
<?php if (!empty($upcoming_matches)): ?>
<section class="bg-gradient-to-r from-green-600 to-blue-600 py-8 mb-8 matches-widget">
    <div class="container mx-auto px-4">
        <div class="bg-white rounded-xl shadow-2xl overflow-hidden relative z-10">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-500 to-blue-500 text-white p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="bg-white bg-opacity-20 rounded-full p-3 ml-4">
                            <i class="fas fa-futbol text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold">مواعيد المباريات</h2>
                            <p class="text-green-100 text-sm">المباريات القادمة</p>
                        </div>
                    </div>
                    <div class="hidden md:flex items-center space-x-4 space-x-reverse">
                        <div class="text-center">
                            <div class="text-2xl font-bold"><?php echo count($upcoming_matches); ?></div>
                            <div class="text-xs text-green-100">مباراة قادمة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Matches Slider -->
            <div class="p-6">
                <div class="matches-slider" id="matchesSlider">
                    <!-- Navigation Buttons -->
                    <button class="slider-nav-btn prev" id="prevBtn" aria-label="المباراة السابقة">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                    <button class="slider-nav-btn next" id="nextBtn" aria-label="المباراة التالية">
                        <i class="fas fa-chevron-left"></i>
                    </button>

                    <!-- Slider Container -->
                    <div class="matches-slider-container" id="sliderContainer">
                        <?php
                        $matches_per_slide = [
                            'desktop' => 3,
                            'tablet' => 2,
                            'mobile' => 1
                        ];

                        $all_matches = array_slice($upcoming_matches, 0, 9); // أقصى 9 مباريات
                        $total_slides = ceil(count($all_matches) / 3); // حساب عدد الشرائح للديسكتوب

                        for ($slide = 0; $slide < $total_slides; $slide++):
                            $slide_matches = array_slice($all_matches, $slide * 3, 3);
                        ?>
                        <div class="matches-slide">
                            <?php foreach ($slide_matches as $match): ?>
                            <div class="match-card <?php echo $match['is_featured'] ? 'featured-match' : 'bg-gray-50'; ?> rounded-lg p-4 border border-gray-200">
                        <!-- Match Date & Competition -->
                        <div class="flex items-center justify-between mb-3">
                            <div class="text-xs text-gray-600 bg-blue-100 px-2 py-1 rounded-full">
                                <i class="fas fa-calendar ml-1"></i>
                                <?php echo formatMatchDate($match['match_date'], 'date_only'); ?>
                            </div>
                            <div class="text-xs text-gray-500">
                                <?php echo mb_substr($match['competition'], 0, 15) . (mb_strlen($match['competition']) > 15 ? '...' : ''); ?>
                            </div>
                        </div>

                        <!-- Teams -->
                        <div class="flex items-center justify-between mb-3">
                            <!-- Home Team -->
                            <div class="flex items-center flex-1">
                                <?php if ($match['home_team_logo']): ?>
                                <img src="<?php echo $match['home_team_logo']; ?>"
                                     alt="<?php echo $match['home_team']; ?>"
                                     class="team-logo w-8 h-8 object-contain ml-2 rounded">
                                <?php else: ?>
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center ml-2">
                                    <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                </div>
                                <?php endif; ?>
                                <span class="text-sm font-semibold text-gray-800 truncate">
                                    <?php echo mb_substr($match['home_team'], 0, 12) . (mb_strlen($match['home_team']) > 12 ? '...' : ''); ?>
                                </span>
                            </div>

                            <!-- VS -->
                            <div class="mx-3">
                                <span class="text-xs text-gray-400 font-bold bg-gray-200 px-2 py-1 rounded">VS</span>
                            </div>

                            <!-- Away Team -->
                            <div class="flex items-center flex-1 justify-end">
                                <span class="text-sm font-semibold text-gray-800 truncate">
                                    <?php echo mb_substr($match['away_team'], 0, 12) . (mb_strlen($match['away_team']) > 12 ? '...' : ''); ?>
                                </span>
                                <?php if ($match['away_team_logo']): ?>
                                <img src="<?php echo $match['away_team_logo']; ?>"
                                     alt="<?php echo $match['away_team']; ?>"
                                     class="team-logo w-8 h-8 object-contain mr-2 rounded">
                                <?php else: ?>
                                <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-2">
                                    <i class="fas fa-shield-alt text-gray-500 text-xs"></i>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Match Time & Status -->
                        <div class="flex items-center justify-between">
                            <div class="text-xs text-gray-600">
                                <i class="fas fa-clock ml-1 text-blue-500"></i>
                                <?php echo formatMatchDate($match['match_date'], 'time_only'); ?>
                            </div>
                            <?php if ($match['venue']): ?>
                            <div class="text-xs text-gray-500 truncate max-w-20">
                                <i class="fas fa-map-marker-alt ml-1"></i>
                                <?php echo mb_substr($match['venue'], 0, 10) . (mb_strlen($match['venue']) > 10 ? '...' : ''); ?>
                            </div>
                            <?php endif; ?>
                        </div>

                                <!-- Featured Match Indicator -->
                                <?php if ($match['is_featured']): ?>
                                <div class="mt-2 text-center">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <i class="fas fa-star ml-1"></i>مباراة مميزة
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endfor; ?>
                    </div>

                    <!-- Slider Dots -->
                    <div class="slider-dots" id="sliderDots">
                        <?php for ($i = 0; $i < $total_slides; $i++): ?>
                        <button class="slider-dot <?php echo $i === 0 ? 'active' : ''; ?>"
                                data-slide="<?php echo $i; ?>"
                                aria-label="الشريحة <?php echo $i + 1; ?>"></button>
                        <?php endfor; ?>
                    </div>
                </div>

                <!-- View All Matches Button -->
                <div class="mt-6 text-center">
                    <a href="matches.php"
                       class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-futbol ml-2"></i>
                        عرض جميع المباريات
                        <i class="fas fa-arrow-left mr-2"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<main class="container mx-auto px-4 py-8">
    <!-- Hero Section with Featured Articles -->
    <?php if (!empty($featured_articles)): ?>
    <section class="mb-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Featured Article -->
            <div class="lg:col-span-2">
                <?php $main_featured = $featured_articles[0]; ?>
                <article class="relative overflow-hidden rounded-xl shadow-lg group">
                    <div class="aspect-w-16 aspect-h-9">
                        <?php if ($main_featured['image_url']): ?>
                            <img src="<?php echo $main_featured['image_url']; ?>" 
                                 alt="<?php echo htmlspecialchars($main_featured['title']); ?>"
                                 class="w-full h-96 object-cover group-hover:scale-105 transition-transform duration-300">
                        <?php else: ?>
                            <div class="w-full h-96 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                <i class="fas fa-newspaper text-6xl text-white opacity-50"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                    <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <div class="mb-2">
                            <span class="bg-red-600 px-3 py-1 rounded-full text-sm font-medium">عاجل</span>
                            <?php if ($main_featured['category_name']): ?>
                                <span class="bg-blue-600 px-3 py-1 rounded-full text-sm font-medium mr-2">
                                    <?php echo $main_featured['category_name']; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        <h2 class="text-2xl font-bold mb-2 leading-tight">
                            <a href="article.php?slug=<?php echo $main_featured['slug']; ?>" 
                               class="hover:text-blue-300 transition-colors">
                                <?php echo $main_featured['title']; ?>
                            </a>
                        </h2>
                        <p class="text-gray-200 mb-3 line-clamp-2">
                            <?php echo $main_featured['excerpt']; ?>
                        </p>
                        <div class="flex items-center text-sm text-gray-300">
                            <i class="fas fa-clock ml-2"></i>
                            <?php echo formatArabicDate($main_featured['published_at']); ?>
                            <span class="mx-2">•</span>
                            <i class="fas fa-eye ml-1"></i>
                            <?php echo number_format($main_featured['views']); ?> مشاهدة
                        </div>
                    </div>
                </article>
            </div>
            
            <!-- Side Featured Articles -->
            <div class="space-y-4">
                <?php for ($i = 1; $i < count($featured_articles) && $i < 4; $i++): 
                    $featured = $featured_articles[$i]; ?>
                <article class="bg-white rounded-lg shadow-md overflow-hidden news-card">
                    <div class="flex">
                        <div class="w-24 h-20 flex-shrink-0">
                            <?php if ($featured['image_url']): ?>
                                <img src="<?php echo $featured['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($featured['title']); ?>"
                                     class="w-full h-full object-cover">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1 p-3">
                            <h3 class="font-semibold text-sm leading-tight mb-1">
                                <a href="article.php?slug=<?php echo $featured['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo mb_substr($featured['title'], 0, 80) . (mb_strlen($featured['title']) > 80 ? '...' : ''); ?>
                                </a>
                            </h3>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatDate($featured['published_at'], 'H:i'); ?>
                            </div>
                        </div>
                    </div>
                </article>
                <?php endfor; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-3">
            <!-- Section Title -->
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-gray-800">
                    <i class="fas fa-newspaper ml-2 text-blue-600"></i>
                    آخر الأخبار
                </h2>
                <div class="flex space-x-2 space-x-reverse">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">
                        الكل
                    </button>
                    <?php foreach (array_slice(getCategories(), 0, 4) as $cat): ?>
                    <a href="category.php?slug=<?php echo $cat['slug']; ?>" 
                       class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg text-sm hover:bg-gray-300 transition-colors">
                        <?php echo $cat['name']; ?>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Articles Grid -->
            <?php if (!empty($articles)): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
                <?php foreach ($articles as $article): ?>
                <article class="bg-white rounded-lg shadow-md overflow-hidden news-card">
                    <div class="aspect-w-16 aspect-h-9">
                        <?php if ($article['image_url']): ?>
                            <img src="<?php echo $article['image_url']; ?>" 
                                 alt="<?php echo htmlspecialchars($article['title']); ?>"
                                 class="w-full h-48 object-cover">
                        <?php else: ?>
                            <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <i class="fas fa-newspaper text-4xl text-gray-400"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="p-4">
                        <div class="flex items-center justify-between mb-2">
                            <?php if ($article['category_name']): ?>
                                <a href="category.php?slug=<?php echo $article['category_slug']; ?>" 
                                   class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium hover:bg-blue-200 transition-colors">
                                    <?php echo $article['category_name']; ?>
                                </a>
                            <?php endif; ?>
                            <span class="text-xs text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatDate($article['published_at'], 'H:i'); ?>
                            </span>
                        </div>
                        <h3 class="font-bold text-lg mb-2 leading-tight">
                            <a href="article.php?slug=<?php echo $article['slug']; ?>" 
                               class="hover:text-blue-600 transition-colors">
                                <?php echo $article['title']; ?>
                            </a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-3 line-clamp-3">
                            <?php echo $article['excerpt']; ?>
                        </p>
                        <div class="flex items-center justify-between text-xs text-gray-500">
                            <span>
                                <i class="fas fa-eye ml-1"></i>
                                <?php echo number_format($article['views']); ?>
                            </span>
                            <span>
                                <?php echo formatArabicDate($article['published_at']); ?>
                            </span>
                        </div>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php echo generatePagination($page, $total_pages, 'index.php'); ?>
            
            <?php else: ?>
            <div class="text-center py-12">
                <i class="fas fa-newspaper text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد أخبار متاحة</h3>
                <p class="text-gray-500">سيتم إضافة الأخبار قريباً</p>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Latest News -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-clock ml-2 text-blue-600"></i>
                    أحدث الأخبار
                </h3>
                <div class="space-y-4">
                    <?php foreach (array_slice($latest_articles, 0, 6) as $latest): ?>
                    <article class="flex space-x-3 space-x-reverse">
                        <div class="w-16 h-12 flex-shrink-0">
                            <?php if ($latest['image_url']): ?>
                                <img src="<?php echo $latest['image_url']; ?>" 
                                     alt="<?php echo htmlspecialchars($latest['title']); ?>"
                                     class="w-full h-full object-cover rounded">
                            <?php else: ?>
                                <div class="w-full h-full bg-gray-200 rounded flex items-center justify-center">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-medium text-sm leading-tight mb-1">
                                <a href="article.php?slug=<?php echo $latest['slug']; ?>" 
                                   class="hover:text-blue-600 transition-colors">
                                    <?php echo mb_substr($latest['title'], 0, 60) . (mb_strlen($latest['title']) > 60 ? '...' : ''); ?>
                                </a>
                            </h4>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatDate($latest['published_at'], 'H:i'); ?>
                            </div>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Upcoming Matches -->
            <?php if (!empty($upcoming_matches)): ?>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-futbol ml-2 text-green-600"></i>
                    المباريات القادمة
                </h3>
                <div class="space-y-4">
                    <?php foreach ($upcoming_matches as $match): ?>
                    <div class="bg-gray-50 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center text-xs text-gray-600">
                                <i class="fas fa-calendar ml-1"></i>
                                <?php echo formatMatchDate($match['match_date'], 'date_only'); ?>
                            </div>
                            <div class="text-xs text-gray-600">
                                <i class="fas fa-clock ml-1"></i>
                                <?php echo formatMatchDate($match['match_date'], 'time_only'); ?>
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center flex-1">
                                <?php if ($match['home_team_logo']): ?>
                                <img src="<?php echo $match['home_team_logo']; ?>" alt="<?php echo $match['home_team']; ?>" class="w-6 h-6 ml-2">
                                <?php endif; ?>
                                <span class="text-sm font-medium text-gray-800"><?php echo mb_substr($match['home_team'], 0, 15); ?></span>
                            </div>

                            <div class="text-xs text-gray-500 mx-2">VS</div>

                            <div class="flex items-center flex-1 justify-end">
                                <span class="text-sm font-medium text-gray-800"><?php echo mb_substr($match['away_team'], 0, 15); ?></span>
                                <?php if ($match['away_team_logo']): ?>
                                <img src="<?php echo $match['away_team_logo']; ?>" alt="<?php echo $match['away_team']; ?>" class="w-6 h-6 mr-2">
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="text-xs text-gray-500 mt-2 text-center">
                            <?php echo $match['competition']; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="mt-4 text-center">
                    <a href="matches.php" class="text-green-600 hover:text-green-800 text-sm font-medium transition-colors">
                        <i class="fas fa-arrow-left ml-1"></i>عرض جميع المباريات
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <!-- Categories -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold mb-4 border-b border-gray-200 pb-2">
                    <i class="fas fa-list ml-2 text-blue-600"></i>
                    التصنيفات
                </h3>
                <div class="space-y-2">
                    <?php foreach (getCategories() as $category): ?>
                    <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                       class="flex items-center justify-between p-2 rounded hover:bg-gray-50 transition-colors">
                        <span class="text-gray-700"><?php echo $category['name']; ?></span>
                        <i class="fas fa-chevron-left text-gray-400"></i>
                    </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- Football Matches Slider JavaScript -->
<?php if (!empty($upcoming_matches)): ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('matchesSlider');
    const sliderContainer = document.getElementById('sliderContainer');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const dots = document.querySelectorAll('.slider-dot');

    if (!slider || !sliderContainer || !prevBtn || !nextBtn) {
        console.warn('Matches slider elements not found');
        return;
    }

    let currentSlide = 0;
    const totalSlides = <?php echo isset($total_slides) ? $total_slides : 1; ?>;
    let autoPlayInterval;
    let isAutoPlaying = true;
    let touchStartX = 0;
    let touchEndX = 0;
    let isDragging = false;

    // Initialize slider
    function initSlider() {
        updateSlider();
        startAutoPlay();

        // Add event listeners
        prevBtn.addEventListener('click', prevSlide);
        nextBtn.addEventListener('click', nextSlide);

        // Dots navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => goToSlide(index));
        });

        // Touch/swipe support
        slider.addEventListener('touchstart', handleTouchStart, { passive: true });
        slider.addEventListener('touchmove', handleTouchMove, { passive: true });
        slider.addEventListener('touchend', handleTouchEnd, { passive: true });

        // Mouse drag support for desktop
        slider.addEventListener('mousedown', handleMouseDown);
        slider.addEventListener('mousemove', handleMouseMove);
        slider.addEventListener('mouseup', handleMouseUp);
        slider.addEventListener('mouseleave', handleMouseUp);

        // Pause auto-play on hover
        slider.addEventListener('mouseenter', pauseAutoPlay);
        slider.addEventListener('mouseleave', resumeAutoPlay);

        // Keyboard navigation
        document.addEventListener('keydown', handleKeyDown);

        // Visibility change (pause when tab is not active)
        document.addEventListener('visibilitychange', handleVisibilityChange);
    }

    // Update slider position
    function updateSlider() {
        const translateX = -currentSlide * 100;
        sliderContainer.style.transform = `translateX(${translateX}%)`;

        // Update dots
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlide);
        });

        // Update navigation buttons
        prevBtn.style.opacity = currentSlide === 0 ? '0.5' : '1';
        nextBtn.style.opacity = currentSlide === totalSlides - 1 ? '0.5' : '1';

        // Add pause indicator if auto-play is paused
        slider.classList.toggle('slider-paused', !isAutoPlaying);
    }

    // Navigation functions
    function nextSlide() {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlider();
        resetAutoPlay();
    }

    function prevSlide() {
        currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
        updateSlider();
        resetAutoPlay();
    }

    function goToSlide(slideIndex) {
        currentSlide = slideIndex;
        updateSlider();
        resetAutoPlay();
    }

    // Auto-play functions
    function startAutoPlay() {
        if (totalSlides <= 1) return;

        autoPlayInterval = setInterval(() => {
            if (isAutoPlaying) {
                nextSlide();
            }
        }, 4000); // 4 seconds
    }

    function pauseAutoPlay() {
        isAutoPlaying = false;
        updateSlider();
    }

    function resumeAutoPlay() {
        isAutoPlaying = true;
        updateSlider();
    }

    function resetAutoPlay() {
        clearInterval(autoPlayInterval);
        startAutoPlay();
    }

    // Touch/swipe handlers
    function handleTouchStart(e) {
        touchStartX = e.touches[0].clientX;
        isDragging = true;
        slider.classList.add('swiping');
        pauseAutoPlay();
    }

    function handleTouchMove(e) {
        if (!isDragging) return;
        touchEndX = e.touches[0].clientX;
    }

    function handleTouchEnd(e) {
        if (!isDragging) return;

        slider.classList.remove('swiping');
        isDragging = false;

        const swipeThreshold = 50;
        const swipeDistance = touchStartX - touchEndX;

        if (Math.abs(swipeDistance) > swipeThreshold) {
            if (swipeDistance > 0) {
                // Swipe left (next slide in RTL)
                prevSlide();
            } else {
                // Swipe right (previous slide in RTL)
                nextSlide();
            }
        }

        setTimeout(resumeAutoPlay, 1000);
    }

    // Mouse drag handlers
    function handleMouseDown(e) {
        touchStartX = e.clientX;
        isDragging = true;
        slider.classList.add('swiping');
        pauseAutoPlay();
        e.preventDefault();
    }

    function handleMouseMove(e) {
        if (!isDragging) return;
        touchEndX = e.clientX;
        e.preventDefault();
    }

    function handleMouseUp(e) {
        if (!isDragging) return;

        slider.classList.remove('swiping');
        isDragging = false;

        const swipeThreshold = 50;
        const swipeDistance = touchStartX - touchEndX;

        if (Math.abs(swipeDistance) > swipeThreshold) {
            if (swipeDistance > 0) {
                nextSlide();
            } else {
                prevSlide();
            }
        }

        setTimeout(resumeAutoPlay, 1000);
    }

    // Keyboard navigation
    function handleKeyDown(e) {
        if (!slider.matches(':hover')) return;

        switch(e.key) {
            case 'ArrowLeft':
                nextSlide(); // In RTL, left arrow goes to next
                e.preventDefault();
                break;
            case 'ArrowRight':
                prevSlide(); // In RTL, right arrow goes to previous
                e.preventDefault();
                break;
            case ' ':
                isAutoPlaying ? pauseAutoPlay() : resumeAutoPlay();
                e.preventDefault();
                break;
        }
    }

    // Handle visibility change
    function handleVisibilityChange() {
        if (document.hidden) {
            pauseAutoPlay();
        } else {
            setTimeout(resumeAutoPlay, 1000);
        }
    }

    // Responsive behavior
    function handleResize() {
        // Recalculate on window resize
        updateSlider();
    }

    window.addEventListener('resize', handleResize);

    // Initialize the slider
    initSlider();

    // Accessibility improvements
    slider.setAttribute('role', 'region');
    slider.setAttribute('aria-label', 'عارض شرائح المباريات');

    // Add ARIA labels to navigation
    prevBtn.setAttribute('aria-label', 'المباراة السابقة');
    nextBtn.setAttribute('aria-label', 'المباراة التالية');

    dots.forEach((dot, index) => {
        dot.setAttribute('aria-label', `الذهاب إلى الشريحة ${index + 1}`);
    });

    console.log('Football matches slider initialized successfully');
});
</script>
<?php endif; ?>

<!-- Back to Top Button -->
<button class="back-to-top" aria-label="العودة للأعلى">
    <i class="fas fa-chevron-up"></i>
</button>

<?php include 'includes/footer.php'; ?>
