    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-16">
        <div class="container mx-auto px-4 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- About Section -->
                <div class="col-span-1 md:col-span-2">
                    <h3 class="text-xl font-bold mb-4">
                        <i class="fas fa-newspaper ml-2 text-blue-400"></i>
                        <?php echo getSetting('site_name', 'موقع الأخبار'); ?>
                    </h3>
                    <p class="text-gray-300 mb-4 leading-relaxed">
                        <?php echo getSetting('site_description', 'موقع إخباري شامل يقدم آخر الأخبار والتطورات من مختلف المجالات بطريقة موثوقة وسريعة.'); ?>
                    </p>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                            <i class="fab fa-youtube text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                            <i class="fab fa-telegram text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">روابط سريعة</h4>
                    <ul class="space-y-2">
                        <li>
                            <a href="index.php" class="text-gray-300 hover:text-white transition-colors">
                                <i class="fas fa-home ml-2"></i>الرئيسية
                            </a>
                        </li>
                        <?php
                        $footer_categories = getCategories();
                        $displayed_categories = array_slice($footer_categories, 0, 5);
                        foreach ($displayed_categories as $category):
                        ?>
                        <li>
                            <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                               class="text-gray-300 hover:text-white transition-colors">
                                <i class="fas fa-chevron-left ml-2"></i><?php echo $category['name']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- Latest Articles -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">أحدث الأخبار</h4>
                    <?php
                    $latest_footer_articles = getLatestArticles(4);
                    foreach ($latest_footer_articles as $article):
                    ?>
                    <div class="mb-3 pb-3 border-b border-gray-700 last:border-b-0">
                        <a href="article.php?slug=<?php echo $article['slug']; ?>" 
                           class="text-gray-300 hover:text-white transition-colors text-sm leading-relaxed">
                            <?php echo mb_substr($article['title'], 0, 60) . (mb_strlen($article['title']) > 60 ? '...' : ''); ?>
                        </a>
                        <div class="text-xs text-gray-500 mt-1">
                            <i class="fas fa-clock ml-1"></i>
                            <?php echo formatArabicDate($article['published_at']); ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Newsletter Subscription -->
            <div class="border-t border-gray-700 mt-8 pt-8">
                <div class="text-center">
                    <h4 class="text-lg font-semibold mb-4">اشترك في النشرة الإخبارية</h4>
                    <p class="text-gray-300 mb-4">احصل على آخر الأخبار مباشرة في بريدك الإلكتروني</p>
                    <form class="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse max-w-md mx-auto">
                        <input type="email" placeholder="أدخل بريدك الإلكتروني" 
                               class="flex-1 px-4 py-2 rounded-lg bg-gray-700 text-white placeholder-gray-400 border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button type="submit" 
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            اشتراك
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Bottom Footer -->
            <div class="border-t border-gray-700 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-gray-400 text-sm mb-4 md:mb-0">
                        <p>&copy; <?php echo date('Y'); ?> <?php echo getSetting('site_name', 'موقع الأخبار'); ?>. جميع الحقوق محفوظة.</p>
                    </div>
                    <div class="flex space-x-6 space-x-reverse text-sm">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">سياسة الخصوصية</a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">شروط الاستخدام</a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">اتصل بنا</a>
                        <a href="admin/login.php" class="text-gray-400 hover:text-white transition-colors">
                            <i class="fas fa-user-shield ml-1"></i>لوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" 
            class="fixed bottom-6 left-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Scripts -->
    <script>
        // Back to top functionality
        const backToTopBtn = document.getElementById('back-to-top');
        
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.remove('opacity-0', 'invisible');
                backToTopBtn.classList.add('opacity-100', 'visible');
            } else {
                backToTopBtn.classList.add('opacity-0', 'invisible');
                backToTopBtn.classList.remove('opacity-100', 'visible');
            }
        });
        
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
        
        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
        
        // Auto-hide messages after 5 seconds
        const messages = document.querySelectorAll('[class*="bg-green-100"], [class*="bg-red-100"], [class*="bg-blue-100"]');
        messages.forEach(message => {
            setTimeout(() => {
                message.style.transition = 'opacity 0.5s ease-out';
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 500);
            }, 5000);
        });
        
        // Newsletter subscription
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            if (email) {
                alert('شكراً لك! تم تسجيل اشتراكك بنجاح.');
                this.reset();
            }
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Add loading animation for external links
        document.querySelectorAll('a[href^="http"]').forEach(link => {
            link.addEventListener('click', function() {
                this.innerHTML += ' <i class="fas fa-spinner fa-spin ml-1"></i>';
            });
        });
    </script>
</body>
</html>
