<?php
/**
 * تهيئة الجلسة والإعدادات الأساسية
 * يجب تحميل هذا الملف قبل أي إخراج HTML
 */

// تفعيل Output Buffering
if (!ob_get_level()) {
    ob_start();
}

// تحديد المسار الجذر
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__));
}

// تحميل ملف التكوين
require_once ROOT_PATH . '/config/config.php';

// بدء الجلسة إذا لم تبدأ بعد
if (session_status() === PHP_SESSION_NONE) {
    // إعدادات الجلسة قبل البدء
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Lax');

    // بدء الجلسة
    session_start();
}

// تحميل ملف الدوال
require_once ROOT_PATH . '/includes/functions.php';

// تجديد معرف الجلسة دورياً لتحسين الأمان
if (!isset($_SESSION['last_regeneration'])) {
    $_SESSION['last_regeneration'] = time();
} elseif (time() - $_SESSION['last_regeneration'] > 300) { // كل 5 دقائق
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}

// دالة للتحقق من صحة الجلسة
function validateSession() {
    // التحقق من انتهاء صلاحية الجلسة
    if (isset($_SESSION['expires']) && $_SESSION['expires'] < time()) {
        session_destroy();
        return false;
    }

    // التحقق من IP Address (اختياري - معطل لتجنب مشاكل Proxy)
    // if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== $_SERVER['REMOTE_ADDR']) {
    //     session_destroy();
    //     return false;
    // }

    return true;
}

// تعيين انتهاء صلاحية الجلسة
if (!isset($_SESSION['expires'])) {
    $_SESSION['expires'] = time() + SESSION_LIFETIME;
    $_SESSION['ip_address'] = $_SERVER['REMOTE_ADDR'];
}

// التحقق من صحة الجلسة
validateSession();

// دالة مساعدة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// دالة مساعدة للحصول على معرف المستخدم
function getUserId() {
    return $_SESSION['user_id'] ?? null;
}

// دالة لتعيين رسالة فلاش
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][] = [
        'type' => $type,
        'message' => $message,
        'timestamp' => time()
    ];
}

// دالة للحصول على رسائل الفلاش
function getFlashMessages($clear = true) {
    $messages = $_SESSION['flash_messages'] ?? [];

    if ($clear) {
        unset($_SESSION['flash_messages']);
    }

    return $messages;
}

// دالة للحماية من CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// إنشاء CSRF Token تلقائياً
generateCSRFToken();
?>