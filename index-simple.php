<?php
/**
 * Simple Working Index Page
 * Use this if the main index.php has issues
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define ROOT_PATH
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}

// Start session safely
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Basic configuration
$site_name = 'موقع الأخبار';
$site_description = 'موقع إخباري شامل';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $site_name; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Tajawal', Arial, sans-serif; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-lg">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <i class="fas fa-newspaper text-2xl text-blue-600 ml-3"></i>
                    <h1 class="text-2xl font-bold text-gray-800"><?php echo $site_name; ?></h1>
                </div>
                <nav class="hidden md:flex space-x-6 space-x-reverse">
                    <a href="index.php" class="text-gray-700 hover:text-blue-600 font-medium">الرئيسية</a>
                    <a href="admin/login.php" class="text-gray-700 hover:text-blue-600 font-medium">لوحة التحكم</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg text-white py-16">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold mb-4">مرحباً بك في <?php echo $site_name; ?></h2>
            <p class="text-xl mb-8"><?php echo $site_description; ?></p>
            <div class="space-x-4 space-x-reverse">
                <a href="setup.php" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    إعداد قاعدة البيانات
                </a>
                <a href="admin/login.php" class="bg-blue-800 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-900 transition-colors">
                    لوحة التحكم
                </a>
            </div>
        </div>
    </section>

    <!-- Status Section -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- PHP Status -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="text-green-600 text-4xl mb-4">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">PHP يعمل بنجاح</h3>
                    <p class="text-gray-600">الإصدار: <?php echo PHP_VERSION; ?></p>
                </div>

                <!-- Server Status -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <div class="text-blue-600 text-4xl mb-4">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">الخادم</h3>
                    <p class="text-gray-600"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></p>
                </div>

                <!-- Database Status -->
                <div class="bg-white rounded-lg shadow-md p-6 text-center">
                    <?php
                    $db_status = false;
                    $db_message = 'غير متصل';
                    
                    try {
                        if (file_exists('config/database.php')) {
                            include_once 'config/database.php';
                            if (class_exists('Database')) {
                                $database = new Database();
                                $db = $database->connect();
                                if ($db) {
                                    $db_status = true;
                                    $db_message = 'متصل';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $db_message = 'خطأ في الاتصال';
                    }
                    ?>
                    <div class="<?php echo $db_status ? 'text-green-600' : 'text-red-600'; ?> text-4xl mb-4">
                        <i class="fas fa-<?php echo $db_status ? 'check-circle' : 'times-circle'; ?>"></i>
                    </div>
                    <h3 class="text-lg font-semibold mb-2">قاعدة البيانات</h3>
                    <p class="text-gray-600"><?php echo $db_message; ?></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Actions -->
    <section class="py-12 bg-gray-100">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl font-bold text-center mb-8">إجراءات سريعة</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="setup.php" class="bg-blue-600 text-white p-4 rounded-lg text-center hover:bg-blue-700 transition-colors">
                    <i class="fas fa-database text-2xl mb-2"></i>
                    <div class="font-medium">إعداد قاعدة البيانات</div>
                </a>
                
                <a href="admin/login.php" class="bg-green-600 text-white p-4 rounded-lg text-center hover:bg-green-700 transition-colors">
                    <i class="fas fa-user-shield text-2xl mb-2"></i>
                    <div class="font-medium">لوحة التحكم</div>
                </a>
                
                <a href="debug-500.php" class="bg-orange-600 text-white p-4 rounded-lg text-center hover:bg-orange-700 transition-colors">
                    <i class="fas fa-bug text-2xl mb-2"></i>
                    <div class="font-medium">أدوات التشخيص</div>
                </a>
                
                <a href="minimal-test.php" class="bg-purple-600 text-white p-4 rounded-lg text-center hover:bg-purple-700 transition-colors">
                    <i class="fas fa-vial text-2xl mb-2"></i>
                    <div class="font-medium">اختبار النظام</div>
                </a>
            </div>
        </div>
    </section>

    <!-- System Information -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl font-bold text-center mb-8">معلومات النظام</h2>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold mb-3">معلومات PHP</h3>
                        <ul class="space-y-2 text-sm">
                            <li><strong>الإصدار:</strong> <?php echo PHP_VERSION; ?></li>
                            <li><strong>الذاكرة المتاحة:</strong> <?php echo ini_get('memory_limit'); ?></li>
                            <li><strong>وقت التنفيذ الأقصى:</strong> <?php echo ini_get('max_execution_time'); ?>s</li>
                            <li><strong>حد الرفع:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold mb-3">معلومات الخادم</h3>
                        <ul class="space-y-2 text-sm">
                            <li><strong>الخادم:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></li>
                            <li><strong>نظام التشغيل:</strong> <?php echo PHP_OS; ?></li>
                            <li><strong>الوقت الحالي:</strong> <?php echo date('Y-m-d H:i:s'); ?></li>
                            <li><strong>المنطقة الزمنية:</strong> <?php echo date_default_timezone_get(); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; <?php echo date('Y'); ?> <?php echo $site_name; ?>. جميع الحقوق محفوظة.</p>
            <div class="mt-4 space-x-4 space-x-reverse">
                <a href="TROUBLESHOOTING_500.md" class="text-gray-300 hover:text-white">دليل استكشاف الأخطاء</a>
                <a href="README.md" class="text-gray-300 hover:text-white">دليل المستخدم</a>
            </div>
        </div>
    </footer>

    <script>
        // Simple JavaScript for interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple news website loaded successfully');
            
            // Add click tracking for debugging
            document.querySelectorAll('a').forEach(link => {
                link.addEventListener('click', function() {
                    console.log('Navigating to:', this.href);
                });
            });
        });
    </script>
</body>
</html>
