<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'includes/matches_functions.php';

session_start();

$page_title = 'مواعيد المباريات';
$page_description = 'جدول مواعيد مباريات كرة القدم - المباريات القادمة والنتائج';

// معالجة الفلاتر
$filters = [];
$current_tab = $_GET['tab'] ?? 'upcoming';

if (isset($_GET['competition']) && !empty($_GET['competition'])) {
    $filters['competition'] = $_GET['competition'];
}

if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    $filters['date_from'] = $_GET['date_from'];
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    $filters['date_to'] = $_GET['date_to'];
}

if (isset($_GET['team']) && !empty($_GET['team'])) {
    $filters['team'] = $_GET['team'];
}

// تحديد نوع المباريات حسب التبويب
switch ($current_tab) {
    case 'live':
        $matches = getLiveMatches();
        break;
    case 'finished':
        $matches = getFinishedMatches(20);
        break;
    case 'all':
        $matches = getMatches(20, 0, $filters);
        break;
    default:
        $matches = getUpcomingMatches(20);
        break;
}

// الحصول على البطولات للفلتر
$competitions = getCompetitions();

// الحصول على الإحصائيات
$stats = getMatchesStats();

include 'includes/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <!-- العنوان الرئيسي -->
    <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">
            <i class="fas fa-futbol ml-2 text-green-600"></i>
            مواعيد المباريات
        </h1>
        <p class="text-gray-600 text-lg">تابع جدول المباريات والنتائج لأهم البطولات</p>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-blue-600"><?php echo $stats['upcoming']; ?></div>
            <div class="text-sm text-blue-800">مباريات قادمة</div>
        </div>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-red-600"><?php echo $stats['live']; ?></div>
            <div class="text-sm text-red-800">مباريات جارية</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-green-600"><?php echo $stats['finished']; ?></div>
            <div class="text-sm text-green-800">مباريات منتهية</div>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-purple-600"><?php echo $stats['total']; ?></div>
            <div class="text-sm text-purple-800">إجمالي المباريات</div>
        </div>
    </div>

    <!-- التبويبات -->
    <div class="bg-white rounded-lg shadow-lg mb-6">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 space-x-reverse px-6">
                <a href="?tab=upcoming" 
                   class="py-4 px-1 border-b-2 font-medium text-sm <?php echo $current_tab === 'upcoming' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                    <i class="fas fa-clock ml-1"></i>المباريات القادمة
                </a>
                <a href="?tab=live" 
                   class="py-4 px-1 border-b-2 font-medium text-sm <?php echo $current_tab === 'live' ? 'border-red-500 text-red-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                    <i class="fas fa-play-circle ml-1"></i>المباريات الجارية
                </a>
                <a href="?tab=finished" 
                   class="py-4 px-1 border-b-2 font-medium text-sm <?php echo $current_tab === 'finished' ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                    <i class="fas fa-check-circle ml-1"></i>النتائج
                </a>
                <a href="?tab=all" 
                   class="py-4 px-1 border-b-2 font-medium text-sm <?php echo $current_tab === 'all' ? 'border-purple-500 text-purple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>">
                    <i class="fas fa-list ml-1"></i>جميع المباريات
                </a>
            </nav>
        </div>

        <!-- الفلاتر -->
        <div class="p-6 bg-gray-50">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <input type="hidden" name="tab" value="<?php echo $current_tab; ?>">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">البطولة</label>
                    <select name="competition" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">جميع البطولات</option>
                        <?php foreach ($competitions as $comp): ?>
                        <option value="<?php echo $comp['name']; ?>" <?php echo (isset($_GET['competition']) && $_GET['competition'] === $comp['name']) ? 'selected' : ''; ?>>
                            <?php echo $comp['name']; ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">من تاريخ</label>
                    <input type="date" name="date_from" value="<?php echo $_GET['date_from'] ?? ''; ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">إلى تاريخ</label>
                    <input type="date" name="date_to" value="<?php echo $_GET['date_to'] ?? ''; ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">البحث عن فريق</label>
                    <input type="text" name="team" value="<?php echo $_GET['team'] ?? ''; ?>" 
                           placeholder="اسم الفريق..." 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="md:col-span-4 flex gap-2">
                    <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-search ml-1"></i>بحث
                    </button>
                    <a href="matches.php?tab=<?php echo $current_tab; ?>" class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors">
                        <i class="fas fa-times ml-1"></i>مسح الفلاتر
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المباريات -->
    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
        <?php if (!empty($matches)): ?>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ والوقت</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">المباراة</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">النتيجة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البطولة</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($matches as $match): ?>
                    <tr class="hover:bg-gray-50 transition-colors">
                        <!-- التاريخ والوقت -->
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">
                                <?php echo formatMatchDate($match['match_date'], 'date_only'); ?>
                            </div>
                            <div class="text-sm text-gray-500">
                                <?php echo formatMatchDate($match['match_date'], 'time_only'); ?>
                            </div>
                        </td>
                        
                        <!-- المباراة -->
                        <td class="px-6 py-4">
                            <div class="flex items-center justify-center space-x-4 space-x-reverse">
                                <!-- الفريق المضيف -->
                                <div class="flex items-center flex-1 justify-end">
                                    <div class="text-right">
                                        <div class="text-sm font-medium text-gray-900"><?php echo $match['home_team']; ?></div>
                                        <?php if ($match['venue']): ?>
                                        <div class="text-xs text-gray-500"><?php echo $match['venue']; ?></div>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($match['home_team_logo']): ?>
                                    <img src="<?php echo $match['home_team_logo']; ?>" alt="<?php echo $match['home_team']; ?>" class="w-8 h-8 mr-2">
                                    <?php endif; ?>
                                </div>
                                
                                <!-- VS -->
                                <div class="text-gray-400 font-bold">VS</div>
                                
                                <!-- الفريق الضيف -->
                                <div class="flex items-center flex-1">
                                    <?php if ($match['away_team_logo']): ?>
                                    <img src="<?php echo $match['away_team_logo']; ?>" alt="<?php echo $match['away_team']; ?>" class="w-8 h-8 ml-2">
                                    <?php endif; ?>
                                    <div class="text-left">
                                        <div class="text-sm font-medium text-gray-900"><?php echo $match['away_team']; ?></div>
                                    </div>
                                </div>
                            </div>
                        </td>
                        
                        <!-- النتيجة -->
                        <td class="px-6 py-4 text-center">
                            <?php if ($match['status'] === 'finished' || $match['status'] === 'live'): ?>
                            <div class="text-lg font-bold text-gray-900">
                                <?php echo $match['home_score'] ?? 0; ?> - <?php echo $match['away_score'] ?? 0; ?>
                            </div>
                            <?php if ($match['status'] === 'live' && $match['match_time']): ?>
                            <div class="text-xs text-red-600 font-medium"><?php echo $match['match_time']; ?>'</div>
                            <?php endif; ?>
                            <?php else: ?>
                            <div class="text-sm text-gray-500">-</div>
                            <?php endif; ?>
                        </td>
                        
                        <!-- البطولة -->
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900"><?php echo $match['competition']; ?></div>
                        </td>
                        
                        <!-- الحالة -->
                        <td class="px-6 py-4 text-center">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo getMatchStatusColor($match['status']); ?>">
                                <?php if ($match['status'] === 'live'): ?>
                                <span class="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"></span>
                                <?php endif; ?>
                                <?php echo getMatchStatusArabic($match['status']); ?>
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-12">
            <i class="fas fa-futbol text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مباريات</h3>
            <p class="text-gray-500">لا توجد مباريات متاحة في هذا القسم حالياً</p>
        </div>
        <?php endif; ?>
    </div>

    <!-- المباريات المميزة -->
    <?php 
    $featured_matches = getFeaturedMatches(3);
    if (!empty($featured_matches) && $current_tab === 'upcoming'): 
    ?>
    <div class="mt-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">
            <i class="fas fa-star ml-2 text-yellow-500"></i>
            المباريات المميزة
        </h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <?php foreach ($featured_matches as $featured): ?>
            <div class="bg-gradient-to-br from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6">
                <div class="text-center">
                    <div class="text-sm text-blue-600 font-medium mb-2"><?php echo $featured['competition']; ?></div>
                    
                    <div class="flex items-center justify-between mb-4">
                        <div class="text-center">
                            <?php if ($featured['home_team_logo']): ?>
                            <img src="<?php echo $featured['home_team_logo']; ?>" alt="<?php echo $featured['home_team']; ?>" class="w-12 h-12 mx-auto mb-2">
                            <?php endif; ?>
                            <div class="text-sm font-medium"><?php echo $featured['home_team']; ?></div>
                        </div>
                        
                        <div class="text-2xl font-bold text-gray-400">VS</div>
                        
                        <div class="text-center">
                            <?php if ($featured['away_team_logo']): ?>
                            <img src="<?php echo $featured['away_team_logo']; ?>" alt="<?php echo $featured['away_team']; ?>" class="w-12 h-12 mx-auto mb-2">
                            <?php endif; ?>
                            <div class="text-sm font-medium"><?php echo $featured['away_team']; ?></div>
                        </div>
                    </div>
                    
                    <div class="text-sm text-gray-600">
                        <?php echo formatMatchDate($featured['match_date'], 'arabic'); ?>
                    </div>
                    
                    <?php if ($featured['venue']): ?>
                    <div class="text-xs text-gray-500 mt-1">
                        <i class="fas fa-map-marker-alt ml-1"></i><?php echo $featured['venue']; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
/* تحسينات CSS إضافية */
.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

/* تحسين عرض الجدول على الأجهزة المحمولة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .table-responsive th,
    .table-responsive td {
        padding: 0.5rem;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
