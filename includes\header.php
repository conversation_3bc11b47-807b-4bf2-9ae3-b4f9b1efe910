
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo getSetting('site_name', 'موقع الأخبار'); ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : getSetting('site_description', 'موقع إخباري شامل'); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Tajawal', 'Arial', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .news-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .breaking-news {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .sticky-header {
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.95);
        }

        /* Breaking News Ticker Styles */
        #breaking-news-ticker {
            position: relative;
            z-index: 40;
            border-bottom: 2px solid rgba(255, 255, 255, 0.2);
        }

        #breaking-news-ticker::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .breaking-news-label {
            position: relative;
            overflow: hidden;
        }

        .breaking-news-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: labelShine 2s infinite;
        }

        @keyframes labelShine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Responsive ticker adjustments */
        @media (max-width: 640px) {
            #breaking-news-ticker .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            #breaking-news-ticker h-12 {
                height: 2.5rem;
            }
        }

        /* Smooth scroll for ticker content */
        #news-marquee {
            will-change: transform;
        }

        /* Enhanced hover effects */
        #ticker-pause:hover,
        #ticker-close:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        /* Football Matches Widget Styles */
        .matches-widget {
            position: relative;
            overflow: hidden;
        }

        .matches-widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            animation: matchesShimmer 4s infinite;
            pointer-events: none;
        }

        @keyframes matchesShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .match-card {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .match-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .match-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.5s;
        }

        .match-card:hover::before {
            left: 100%;
        }

        .team-logo {
            transition: transform 0.3s ease;
        }

        .match-card:hover .team-logo {
            transform: scale(1.1);
        }

        .featured-match {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            animation: featuredPulse 2s infinite;
        }

        @keyframes featuredPulse {
            0%, 100% { box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.4); }
            50% { box-shadow: 0 0 0 10px rgba(245, 158, 11, 0); }
        }

        /* Football Matches Slider Styles */
        .matches-slider {
            position: relative;
            overflow: hidden;
            border-radius: 0.75rem;
        }

        .matches-slider-container {
            display: flex;
            transition: transform 0.5s ease-in-out;
            will-change: transform;
        }

        .matches-slide {
            min-width: 100%;
            display: flex;
            gap: 1rem;
            padding: 0 0.5rem;
        }

        /* Responsive slide layouts */
        @media (min-width: 1024px) {
            .matches-slide .match-card {
                flex: 0 0 calc(33.333% - 0.667rem);
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .matches-slide .match-card {
                flex: 0 0 calc(50% - 0.5rem);
            }
        }

        @media (max-width: 767px) {
            .matches-slide .match-card {
                flex: 0 0 100%;
            }
        }

        /* Navigation buttons */
        .slider-nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .slider-nav-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }

        .slider-nav-btn:active {
            transform: translateY(-50%) scale(0.95);
        }

        .slider-nav-btn.prev {
            left: 15px;
        }

        .slider-nav-btn.next {
            right: 15px;
        }

        .slider-nav-btn i {
            color: #374151;
            font-size: 1.2rem;
        }

        /* Hide navigation on very small screens */
        @media (max-width: 480px) {
            .slider-nav-btn {
                width: 40px;
                height: 40px;
            }

            .slider-nav-btn.prev {
                left: 10px;
            }

            .slider-nav-btn.next {
                right: 10px;
            }
        }

        /* Dots indicator */
        .slider-dots {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .slider-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(156, 163, 175, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
        }

        .slider-dot:hover {
            background: rgba(156, 163, 175, 0.8);
            transform: scale(1.2);
        }

        .slider-dot.active {
            background: linear-gradient(135deg, #10b981, #3b82f6);
            transform: scale(1.3);
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.3);
        }

        /* Touch/swipe indicators */
        .matches-slider.swiping {
            cursor: grabbing;
        }

        .matches-slider.swiping .matches-slider-container {
            transition: none;
        }

        /* Auto-play pause indicator */
        .slider-paused::after {
            content: '';
            position: absolute;
            top: 10px;
            right: 10px;
            width: 8px;
            height: 8px;
            background: #f59e0b;
            border-radius: 50%;
            animation: pausePulse 1s infinite;
        }

        @keyframes pausePulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Enhanced match card for slider */
        .matches-slider .match-card {
            margin-bottom: 0;
            height: auto;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        /* Smooth loading animation */
        .matches-slider-loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .matches-slider-loading::after {
            content: '';
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <!-- Homepage Enhancements CSS -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <link rel="stylesheet" href="assets/css/homepage-enhancements.css">
    <?php endif; ?>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="sticky top-0 z-50 sticky-header shadow-lg">
        <div class="container mx-auto px-4">
            <!-- Top Bar -->
            <div class="border-b border-gray-200 py-2">
                <div class="flex justify-between items-center text-sm text-gray-600">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <span><i class="fas fa-calendar-alt ml-1"></i><?php echo formatArabicDate(date('Y-m-d H:i:s')); ?></span>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="#" class="hover:text-blue-600"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="hover:text-blue-600"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="hover:text-blue-600"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="hover:text-blue-600"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
            </div>
            
            <!-- Main Header -->
            <div class="py-4">
                <div class="flex justify-between items-center">
                    <!-- Logo -->
                    <div class="flex items-center">
                        <a href="index.php" class="text-2xl font-bold text-gray-800 hover:text-blue-600 transition-colors">
                            <i class="fas fa-newspaper ml-2 text-blue-600"></i>
                            <?php echo getSetting('site_name', 'موقع الأخبار'); ?>
                        </a>
                    </div>
                    
                    <!-- Search -->
                    <div class="hidden md:block">
                        <form action="search.php" method="GET" class="flex">
                            <input type="text" name="q" placeholder="البحث في الأخبار..." 
                                   value="<?php echo isset($_GET['q']) ? htmlspecialchars($_GET['q']) : ''; ?>"
                                   class="px-4 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-l-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-search"></i>
                            </button>
                        </form>
                    </div>
                    
                    <!-- Mobile Menu Button -->
                    <button id="mobile-menu-btn" class="md:hidden text-gray-600 hover:text-gray-800">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Navigation -->
            <nav class="border-t border-gray-200">
                <div class="py-3">
                    <ul id="main-nav" class="hidden md:flex space-x-8 space-x-reverse">
                        <li>
                            <a href="index.php" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-blue-600">
                                <i class="fas fa-home ml-1"></i>الرئيسية
                            </a>
                        </li>
                        <li>
                            <a href="matches.php" class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-blue-600">
                                <i class="fas fa-futbol ml-1"></i>مواعيد المباريات
                            </a>
                        </li>
                        <?php
                        $categories = getCategories();
                        foreach ($categories as $category):
                        ?>
                        <li>
                            <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                               class="text-gray-700 hover:text-blue-600 font-medium transition-colors py-2 border-b-2 border-transparent hover:border-blue-600">
                                <?php echo $category['name']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    
                    <!-- Mobile Navigation -->
                    <div id="mobile-nav" class="md:hidden hidden mt-4">
                        <ul class="space-y-2">
                            <li>
                                <a href="index.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                                    <i class="fas fa-home ml-2"></i>الرئيسية
                                </a>
                            </li>
                            <li>
                                <a href="matches.php" class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                                    <i class="fas fa-futbol ml-2"></i>مواعيد المباريات
                                </a>
                            </li>
                            <?php foreach ($categories as $category): ?>
                            <li>
                                <a href="category.php?slug=<?php echo $category['slug']; ?>" 
                                   class="block px-4 py-2 text-gray-700 hover:bg-gray-100 rounded">
                                    <?php echo $category['name']; ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <!-- Mobile Search -->
                        <div class="mt-4 px-4">
                            <form action="search.php" method="GET" class="flex">
                                <input type="text" name="q" placeholder="البحث في الأخبار..." 
                                       class="flex-1 px-3 py-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-l-lg hover:bg-blue-700">
                                    <i class="fas fa-search"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Breaking News Ticker -->
    <?php
    // جلب آخر 8 أخبار مميزة أو أحدث الأخبار
    $breaking_news = getFeaturedArticles(8);
    if (empty($breaking_news)) {
        // إذا لم توجد أخبار مميزة، جلب أحدث الأخبار
        $breaking_news = getLatestArticles(8);
    }

    if (!empty($breaking_news)):
    ?>
    <div id="breaking-news-ticker" class="bg-gradient-to-r from-red-600 to-red-700 text-white shadow-lg relative overflow-hidden">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10">
            <div class="container mx-auto px-4">
                <div class="flex items-center h-12">
                    <!-- Breaking News Label -->
                    <div class="breaking-news-label flex items-center bg-white text-red-600 px-4 py-2 rounded-full font-bold text-sm ml-4 shadow-lg flex-shrink-0">
                        <i class="fas fa-bolt ml-2 animate-pulse"></i>
                        <span class="hidden sm:inline">أخبار عاجلة</span>
                        <span class="sm:hidden">عاجل</span>
                    </div>

                    <!-- News Content -->
                    <div class="flex-1 overflow-hidden relative">
                        <div id="news-marquee" class="whitespace-nowrap">
                            <?php
                            $news_items = [];
                            foreach ($breaking_news as $index => $news):
                                $news_items[] = '<a href="article.php?slug=' . $news['slug'] . '" class="inline-block hover:text-yellow-300 transition-colors duration-300 mx-8 font-medium">' .
                                               '<i class="fas fa-circle text-xs ml-2 text-yellow-400"></i>' .
                                               htmlspecialchars($news['title']) . '</a>';
                            endforeach;
                            echo implode('', $news_items);
                            ?>
                        </div>
                    </div>

                    <!-- Controls -->
                    <div class="flex items-center space-x-2 space-x-reverse ml-4 flex-shrink-0">
                        <button id="ticker-pause" class="text-white hover:text-yellow-300 transition-colors p-1" title="إيقاف/تشغيل">
                            <i class="fas fa-pause text-sm"></i>
                        </button>
                        <button id="ticker-close" class="text-white hover:text-red-300 transition-colors p-1" title="إغلاق">
                            <i class="fas fa-times text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Messages -->
    <?php
    $message = getMessage();
    if ($message):
        $bg_color = $message['type'] === 'success' ? 'bg-green-100 border-green-500 text-green-700' : 
                   ($message['type'] === 'error' ? 'bg-red-100 border-red-500 text-red-700' : 
                    'bg-blue-100 border-blue-500 text-blue-700');
    ?>
    <div class="<?php echo $bg_color; ?> border-l-4 p-4 mb-4">
        <div class="container mx-auto px-4">
            <?php echo $message['message']; ?>
        </div>
    </div>
    <?php endif; ?>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileNav = document.getElementById('mobile-nav');
            mobileNav.classList.toggle('hidden');
        });

        // Breaking News Ticker Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const ticker = document.getElementById('breaking-news-ticker');
            const marquee = document.getElementById('news-marquee');
            const pauseBtn = document.getElementById('ticker-pause');
            const closeBtn = document.getElementById('ticker-close');

            if (!ticker || !marquee) return;

            let isPlaying = true;
            let animationId;
            let currentPosition = 0;
            const speed = 1; // pixels per frame

            // Create enhanced marquee animation
            function createMarqueeAnimation() {
                const style = document.createElement('style');
                style.id = 'marquee-styles';
                style.textContent = `
                    @keyframes smoothMarquee {
                        0% { transform: translateX(100%); }
                        100% { transform: translateX(-100%); }
                    }

                    .news-marquee-animate {
                        animation: smoothMarquee 45s linear infinite;
                    }

                    .news-marquee-paused {
                        animation-play-state: paused;
                    }

                    .news-marquee-hover {
                        animation-play-state: paused;
                    }

                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                        .news-marquee-animate {
                            animation-duration: 35s;
                        }
                    }

                    /* Smooth transitions */
                    #news-marquee a {
                        transition: all 0.3s ease;
                    }

                    #news-marquee a:hover {
                        text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
                        transform: scale(1.05);
                    }
                `;
                document.head.appendChild(style);
            }

            // Initialize animation
            function initAnimation() {
                createMarqueeAnimation();
                marquee.classList.add('news-marquee-animate');

                // Pause on hover
                marquee.addEventListener('mouseenter', function() {
                    if (isPlaying) {
                        marquee.classList.add('news-marquee-hover');
                    }
                });

                marquee.addEventListener('mouseleave', function() {
                    marquee.classList.remove('news-marquee-hover');
                });
            }

            // Pause/Play functionality
            if (pauseBtn) {
                pauseBtn.addEventListener('click', function() {
                    const icon = this.querySelector('i');

                    if (isPlaying) {
                        marquee.classList.add('news-marquee-paused');
                        icon.className = 'fas fa-play text-sm';
                        this.title = 'تشغيل';
                        isPlaying = false;
                    } else {
                        marquee.classList.remove('news-marquee-paused');
                        icon.className = 'fas fa-pause text-sm';
                        this.title = 'إيقاف';
                        isPlaying = true;
                    }
                });
            }

            // Close functionality
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    ticker.style.transform = 'translateY(-100%)';
                    ticker.style.transition = 'transform 0.5s ease-out';

                    setTimeout(() => {
                        ticker.style.display = 'none';
                        // Save preference in localStorage
                        localStorage.setItem('breakingNewsHidden', 'true');
                    }, 500);
                });
            }

            // Check if user previously closed the ticker
            if (localStorage.getItem('breakingNewsHidden') === 'true') {
                // Auto-show again after 1 hour
                const hiddenTime = localStorage.getItem('breakingNewsHiddenTime');
                const currentTime = new Date().getTime();

                if (!hiddenTime || (currentTime - parseInt(hiddenTime)) > 3600000) {
                    localStorage.removeItem('breakingNewsHidden');
                    localStorage.removeItem('breakingNewsHiddenTime');
                } else {
                    ticker.style.display = 'none';
                }
            }

            // Save hidden time when closing
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    localStorage.setItem('breakingNewsHiddenTime', new Date().getTime().toString());
                });
            }

            // Initialize the animation
            initAnimation();

            // Auto-refresh news content every 5 minutes
            setInterval(function() {
                if (document.visibilityState === 'visible') {
                    // Only refresh if page is visible
                    fetch(window.location.href)
                        .then(response => response.text())
                        .then(html => {
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(html, 'text/html');
                            const newMarquee = doc.getElementById('news-marquee');

                            if (newMarquee && newMarquee.innerHTML !== marquee.innerHTML) {
                                marquee.innerHTML = newMarquee.innerHTML;
                                // Restart animation with new content
                                marquee.classList.remove('news-marquee-animate');
                                setTimeout(() => {
                                    marquee.classList.add('news-marquee-animate');
                                }, 100);
                            }
                        })
                        .catch(error => {
                            console.log('Failed to refresh breaking news:', error);
                        });
                }
            }, 300000); // 5 minutes
        });
    </script>

    <!-- Homepage Enhancements JavaScript -->
    <?php if (basename($_SERVER['PHP_SELF']) == 'index.php'): ?>
    <script src="assets/js/homepage-enhancements.js"></script>
    <?php endif; ?>

